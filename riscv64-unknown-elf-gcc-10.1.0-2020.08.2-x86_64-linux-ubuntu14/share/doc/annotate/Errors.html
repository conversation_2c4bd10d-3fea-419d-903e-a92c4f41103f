<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1994-2020 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with no
Invariant Sections, with no Front-Cover Texts, and with no Back-Cover
Texts.  A copy of the license is included in the section entitled "GNU
Free Documentation License". -->
<!-- Created by GNU Texinfo 6.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>GDB&rsquo;s Obsolete Annotations: Errors</title>

<meta name="description" content="GDB&rsquo;s Obsolete Annotations: Errors">
<meta name="keywords" content="GDB&rsquo;s Obsolete Annotations: Errors">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="index.html#Top" rel="up" title="Top">
<link href="Breakpoint-Info.html#Breakpoint-Info" rel="next" title="Breakpoint Info">
<link href="Prompting.html#Prompting" rel="prev" title="Prompting">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.indentedblock {margin-right: 0em}
blockquote.smallindentedblock {margin-right: 0em; font-size: smaller}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smalllisp {margin-left: 3.2em}
kbd {font-style: oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nolinebreak {white-space: nowrap}
span.roman {font-family: initial; font-weight: normal}
span.sansserif {font-family: sans-serif; font-weight: normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en">
<a name="Errors"></a>
<div class="header">
<p>
Next: <a href="Breakpoint-Info.html#Breakpoint-Info" accesskey="n" rel="next">Breakpoint Info</a>, Previous: <a href="Prompting.html#Prompting" accesskey="p" rel="prev">Prompting</a>, Up: <a href="index.html#Top" accesskey="u" rel="up">Top</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>]</p>
</div>
<hr>
<a name="Errors-1"></a>
<h2 class="chapter">9 Errors</h2>
<a name="index-annotations-for-errors_002c-warnings-and-interrupts"></a>

<a name="index-quit"></a>
<div class="smallexample">
<pre class="smallexample">^Z^Zquit
</pre></div>

<p>This annotation occurs right before <small>GDB</small> responds to an interrupt.
</p>
<a name="index-error"></a>
<div class="smallexample">
<pre class="smallexample">^Z^Zerror
</pre></div>

<p>This annotation occurs right before <small>GDB</small> responds to an error.
</p>
<p>Quit and error annotations indicate that any annotations which <small>GDB</small> was
in the middle of may end abruptly.  For example, if a
<code>value-history-begin</code> annotation is followed by a <code>error</code>, one
cannot expect to receive the matching <code>value-history-end</code>.  One
cannot expect not to receive it either, however; an error annotation
does not necessarily mean that <small>GDB</small> is immediately returning all the way
to the top level.
</p>
<a name="index-error_002dbegin"></a>
<p>A quit or error annotation may be preceded by
</p>
<div class="smallexample">
<pre class="smallexample">^Z^Zerror-begin
</pre></div>

<p>Any output between that and the quit or error annotation is the error
message.
</p>
<p>Warning messages are not yet annotated.
</p>



</body>
</html>
