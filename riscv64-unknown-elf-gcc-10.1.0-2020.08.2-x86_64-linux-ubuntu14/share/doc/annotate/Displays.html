<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1994-2020 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with no
Invariant Sections, with no Front-Cover Texts, and with no Back-Cover
Texts.  A copy of the license is included in the section entitled "GNU
Free Documentation License". -->
<!-- Created by GNU Texinfo 6.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>GDB&rsquo;s Obsolete Annotations: Displays</title>

<meta name="description" content="GDB&rsquo;s Obsolete Annotations: Displays">
<meta name="keywords" content="GDB&rsquo;s Obsolete Annotations: Displays">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="index.html#Top" rel="up" title="Top">
<link href="Prompting.html#Prompting" rel="next" title="Prompting">
<link href="Frame-Annotations.html#Frame-Annotations" rel="prev" title="Frame Annotations">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.indentedblock {margin-right: 0em}
blockquote.smallindentedblock {margin-right: 0em; font-size: smaller}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smalllisp {margin-left: 3.2em}
kbd {font-style: oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nolinebreak {white-space: nowrap}
span.roman {font-family: initial; font-weight: normal}
span.sansserif {font-family: sans-serif; font-weight: normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en">
<a name="Displays"></a>
<div class="header">
<p>
Next: <a href="Prompting.html#Prompting" accesskey="n" rel="next">Prompting</a>, Previous: <a href="Frame-Annotations.html#Frame-Annotations" accesskey="p" rel="prev">Frame Annotations</a>, Up: <a href="index.html#Top" accesskey="u" rel="up">Top</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>]</p>
</div>
<hr>
<a name="Displays-1"></a>
<h2 class="chapter">7 Displays</h2>

<p><em>Display Annotations have been removed.  <small>GDB/MI</small> instead
provides Variable Objects.</em>
</p>
<a name="index-display_002dbegin"></a>
<a name="index-display_002dnumber_002dend"></a>
<a name="index-display_002dformat"></a>
<a name="index-display_002dexpression"></a>
<a name="index-display_002dexpression_002dend"></a>
<a name="index-display_002dvalue"></a>
<a name="index-display_002dend"></a>
<a name="index-annotations-for-display"></a>
<p>When <small>GDB</small> is told to display something using the <code>display</code> command,
the results of the display are annotated:
</p>
<div class="smallexample">
<pre class="smallexample">^Z^Zdisplay-begin
<var>number</var>
^Z^Zdisplay-number-end
<var>number-separator</var>
^Z^Zdisplay-format
<var>format</var>
^Z^Zdisplay-expression
<var>expression</var>
^Z^Zdisplay-expression-end
<var>expression-separator</var>
^Z^Zdisplay-value
<var>value</var>
^Z^Zdisplay-end
</pre></div>

<p>where <var>number</var> is the number of the display, <var>number-separator</var>
is intended to separate the number from what follows for the user,
<var>format</var> includes information such as the size, format, or other
information about how the value is being displayed, <var>expression</var> is
the expression being displayed, <var>expression-separator</var> is intended
to separate the expression from the text that follows for the user,
and <var>value</var> is the actual value being displayed.
</p>



</body>
</html>
