<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1994-2020 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with no
Invariant Sections, with no Front-Cover Texts, and with no Back-Cover
Texts.  A copy of the license is included in the section entitled "GNU
Free Documentation License". -->
<!-- Created by GNU Texinfo 6.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>GDB&rsquo;s Obsolete Annotations: Multi-threaded Apps</title>

<meta name="description" content="GDB&rsquo;s Obsolete Annotations: Multi-threaded Apps">
<meta name="keywords" content="GDB&rsquo;s Obsolete Annotations: Multi-threaded Apps">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="index.html#Top" rel="up" title="Top">
<link href="GNU-Free-Documentation-License.html#GNU-Free-Documentation-License" rel="next" title="GNU Free Documentation License">
<link href="Source-Annotations.html#Source-Annotations" rel="prev" title="Source Annotations">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.indentedblock {margin-right: 0em}
blockquote.smallindentedblock {margin-right: 0em; font-size: smaller}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smalllisp {margin-left: 3.2em}
kbd {font-style: oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nolinebreak {white-space: nowrap}
span.roman {font-family: initial; font-weight: normal}
span.sansserif {font-family: sans-serif; font-weight: normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en">
<a name="Multi_002dthreaded-Apps"></a>
<div class="header">
<p>
Next: <a href="GNU-Free-Documentation-License.html#GNU-Free-Documentation-License" accesskey="n" rel="next">GNU Free Documentation License</a>, Previous: <a href="Source-Annotations.html#Source-Annotations" accesskey="p" rel="prev">Source Annotations</a>, Up: <a href="index.html#Top" accesskey="u" rel="up">Top</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>]</p>
</div>
<hr>
<a name="Multi_002dthreaded-Applications"></a>
<h2 class="chapter">14 Multi-threaded Applications</h2>
<a name="index-annotations-for-multi_002dthreaded-apps"></a>

<p>The following annotations report thread related changes of state.
</p>
<dl compact="compact">
<dd><a name="index-new_002dthread_002c-annotation"></a>
</dd>
<dt><code>^Z^Znew-thread</code></dt>
<dd>
<p>This annotation is issued once for each thread that is created apart from
the main thread, which is not reported.
</p>
<a name="index-thread_002dchanged_002c-annotation"></a>
</dd>
<dt><code>^Z^Zthread-changed</code></dt>
<dd>
<p>The selected thread has changed.  This may occur at the request of the
user with the <code>thread</code> command, or as a result of execution,
e.g., another thread hits a breakpoint.
</p>
<a name="index-thread_002dexited_002c-annotation"></a>
</dd>
<dt><code>^Z^Zthread-exited,id=&quot;<var>id</var>&quot;,group-id=&quot;<var>gid</var>&quot;</code></dt>
<dd>
<p>This annotation is issued once for each thread that exits.  The <var>id</var>
field contains the global <small>GDB</small> identifier of the thread.  The
<var>gid</var> field identifies the thread group this thread belongs to.
</p>
</dd>
</dl>




</body>
</html>
