<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1994-2020 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with no
Invariant Sections, with no Front-Cover Texts, and with no Back-Cover
Texts.  A copy of the license is included in the section entitled "GNU
Free Documentation License". -->
<!-- Created by GNU Texinfo 6.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>GDB&rsquo;s Obsolete Annotations: Migrating to GDB/MI</title>

<meta name="description" content="GDB&rsquo;s Obsolete Annotations: Migrating to GDB/MI">
<meta name="keywords" content="GDB&rsquo;s Obsolete Annotations: Migrating to GDB/MI">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="index.html#Top" rel="up" title="Top">
<link href="Server-Prefix.html#Server-Prefix" rel="next" title="Server Prefix">
<link href="Limitations.html#Limitations" rel="prev" title="Limitations">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.indentedblock {margin-right: 0em}
blockquote.smallindentedblock {margin-right: 0em; font-size: smaller}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smalllisp {margin-left: 3.2em}
kbd {font-style: oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nolinebreak {white-space: nowrap}
span.roman {font-family: initial; font-weight: normal}
span.sansserif {font-family: sans-serif; font-weight: normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en">
<a name="Migrating-to-GDB_002fMI"></a>
<div class="header">
<p>
Next: <a href="Server-Prefix.html#Server-Prefix" accesskey="n" rel="next">Server Prefix</a>, Previous: <a href="Limitations.html#Limitations" accesskey="p" rel="prev">Limitations</a>, Up: <a href="index.html#Top" accesskey="u" rel="up">Top</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>]</p>
</div>
<hr>
<a name="Migrating-to-GDB_002fMI-1"></a>
<h2 class="chapter">3 Migrating to <small>GDB/MI</small></h2>

<p>By using the &lsquo;<samp>interp mi</samp>&rsquo; command, it is possible for annotation
clients to invoke <small>GDB/MI</small> commands, and hence access the
<small>GDB/MI</small>.  By doing this, existing annotation clients have a
migration path from this obsolete interface to <small>GDB/MI</small>.
</p>



</body>
</html>
