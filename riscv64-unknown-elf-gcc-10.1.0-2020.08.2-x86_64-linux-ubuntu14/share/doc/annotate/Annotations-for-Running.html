<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1994-2020 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with no
Invariant Sections, with no Front-Cover Texts, and with no Back-Cover
Texts.  A copy of the license is included in the section entitled "GNU
Free Documentation License". -->
<!-- Created by GNU Texinfo 6.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>GDB&rsquo;s Obsolete Annotations: Annotations for Running</title>

<meta name="description" content="GDB&rsquo;s Obsolete Annotations: Annotations for Running">
<meta name="keywords" content="GDB&rsquo;s Obsolete Annotations: Annotations for Running">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="index.html#Top" rel="up" title="Top">
<link href="Source-Annotations.html#Source-Annotations" rel="next" title="Source Annotations">
<link href="Invalidation.html#Invalidation" rel="prev" title="Invalidation">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.indentedblock {margin-right: 0em}
blockquote.smallindentedblock {margin-right: 0em; font-size: smaller}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smalllisp {margin-left: 3.2em}
kbd {font-style: oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nolinebreak {white-space: nowrap}
span.roman {font-family: initial; font-weight: normal}
span.sansserif {font-family: sans-serif; font-weight: normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en">
<a name="Annotations-for-Running"></a>
<div class="header">
<p>
Next: <a href="Source-Annotations.html#Source-Annotations" accesskey="n" rel="next">Source Annotations</a>, Previous: <a href="Invalidation.html#Invalidation" accesskey="p" rel="prev">Invalidation</a>, Up: <a href="index.html#Top" accesskey="u" rel="up">Top</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>]</p>
</div>
<hr>
<a name="Running-the-Program"></a>
<h2 class="chapter">12 Running the Program</h2>
<a name="index-annotations-for-running-programs"></a>

<a name="index-starting"></a>
<a name="index-stopping"></a>
<p>When the program starts executing due to a <small>GDB</small> command such as
<code>step</code> or <code>continue</code>, 
</p>
<div class="smallexample">
<pre class="smallexample">^Z^Zstarting
</pre></div>

<p>is output.  When the program stops, 
</p>
<div class="smallexample">
<pre class="smallexample">^Z^Zstopped
</pre></div>

<p>is output.  Before the <code>stopped</code> annotation, a variety of
annotations describe how the program stopped.
</p>
<dl compact="compact">
<dd><a name="index-exited"></a>
</dd>
<dt><code>^Z^Zexited <var>exit-status</var></code></dt>
<dd><p>The program exited, and <var>exit-status</var> is the exit status (zero for
successful exit, otherwise nonzero).
</p>
<a name="index-signalled"></a>
<a name="index-signal_002dname"></a>
<a name="index-signal_002dname_002dend"></a>
<a name="index-signal_002dstring"></a>
<a name="index-signal_002dstring_002dend"></a>
</dd>
<dt><code>^Z^Zsignalled</code></dt>
<dd><p>The program exited with a signal.  After the <code>^Z^Zsignalled</code>, the
annotation continues:
</p>
<div class="smallexample">
<pre class="smallexample"><var>intro-text</var>
^Z^Zsignal-name
<var>name</var>
^Z^Zsignal-name-end
<var>middle-text</var>
^Z^Zsignal-string
<var>string</var>
^Z^Zsignal-string-end
<var>end-text</var>
</pre></div>

<p>where <var>name</var> is the name of the signal, such as <code>SIGILL</code> or
<code>SIGSEGV</code>, and <var>string</var> is the explanation of the signal, such
as <code>Illegal Instruction</code> or <code>Segmentation fault</code>.
<var>intro-text</var>, <var>middle-text</var>, and <var>end-text</var> are for the
user&rsquo;s benefit and have no particular format.
</p>
<a name="index-signal"></a>
</dd>
<dt><code>^Z^Zsignal</code></dt>
<dd><p>The syntax of this annotation is just like <code>signalled</code>, but <small>GDB</small> is
just saying that the program received the signal, not that it was
terminated with it.
</p>
<a name="index-breakpoint"></a>
</dd>
<dt><code>^Z^Zbreakpoint <var>number</var></code></dt>
<dd><p>The program hit breakpoint number <var>number</var>.
</p>
<a name="index-watchpoint"></a>
</dd>
<dt><code>^Z^Zwatchpoint <var>number</var></code></dt>
<dd><p>The program hit watchpoint number <var>number</var>.
</p></dd>
</dl>




</body>
</html>
