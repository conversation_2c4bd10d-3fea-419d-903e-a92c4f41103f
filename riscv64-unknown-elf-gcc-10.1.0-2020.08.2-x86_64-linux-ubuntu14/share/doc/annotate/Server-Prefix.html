<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1994-2020 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with no
Invariant Sections, with no Front-Cover Texts, and with no Back-Cover
Texts.  A copy of the license is included in the section entitled "GNU
Free Documentation License". -->
<!-- Created by GNU Texinfo 6.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>GDB&rsquo;s Obsolete Annotations: Server Prefix</title>

<meta name="description" content="GDB&rsquo;s Obsolete Annotations: Server Prefix">
<meta name="keywords" content="GDB&rsquo;s Obsolete Annotations: Server Prefix">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="index.html#Top" rel="up" title="Top">
<link href="Value-Annotations.html#Value-Annotations" rel="next" title="Value Annotations">
<link href="Migrating-to-GDB_002fMI.html#Migrating-to-GDB_002fMI" rel="prev" title="Migrating to GDB/MI">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.indentedblock {margin-right: 0em}
blockquote.smallindentedblock {margin-right: 0em; font-size: smaller}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smalllisp {margin-left: 3.2em}
kbd {font-style: oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nolinebreak {white-space: nowrap}
span.roman {font-family: initial; font-weight: normal}
span.sansserif {font-family: sans-serif; font-weight: normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en">
<a name="Server-Prefix"></a>
<div class="header">
<p>
Next: <a href="Value-Annotations.html#Value-Annotations" accesskey="n" rel="next">Value Annotations</a>, Previous: <a href="Migrating-to-GDB_002fMI.html#Migrating-to-GDB_002fMI" accesskey="p" rel="prev">Migrating to GDB/MI</a>, Up: <a href="index.html#Top" accesskey="u" rel="up">Top</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>]</p>
</div>
<hr>
<a name="The-Server-Prefix"></a>
<h2 class="chapter">4 The Server Prefix</h2>
<a name="index-server-prefix-for-annotations"></a>

<p>To issue a command to <small>GDB</small> without affecting certain aspects of
the state which is seen by users, prefix it with &lsquo;<samp>server </samp>&rsquo;.  This
means that this command will not affect the command history, nor will it
affect <small>GDB</small>&rsquo;s notion of which command to repeat if <tt class="key">RET</tt> is
pressed on a line by itself.
</p>
<p>The server prefix does not affect the recording of values into the value
history; to print a value without recording it into the value history,
use the <code>output</code> command instead of the <code>print</code> command.
</p>



</body>
</html>
