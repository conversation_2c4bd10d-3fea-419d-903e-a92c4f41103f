<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1994-2020 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with no
Invariant Sections, with no Front-Cover Texts, and with no Back-Cover
Texts.  A copy of the license is included in the section entitled "GNU
Free Documentation License". -->
<!-- Created by GNU Texinfo 6.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>GDB&rsquo;s Obsolete Annotations: Source Annotations</title>

<meta name="description" content="GDB&rsquo;s Obsolete Annotations: Source Annotations">
<meta name="keywords" content="GDB&rsquo;s Obsolete Annotations: Source Annotations">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="index.html#Top" rel="up" title="Top">
<link href="Multi_002dthreaded-Apps.html#Multi_002dthreaded-Apps" rel="next" title="Multi-threaded Apps">
<link href="Annotations-for-Running.html#Annotations-for-Running" rel="prev" title="Annotations for Running">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.indentedblock {margin-right: 0em}
blockquote.smallindentedblock {margin-right: 0em; font-size: smaller}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smalllisp {margin-left: 3.2em}
kbd {font-style: oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nolinebreak {white-space: nowrap}
span.roman {font-family: initial; font-weight: normal}
span.sansserif {font-family: sans-serif; font-weight: normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en">
<a name="Source-Annotations"></a>
<div class="header">
<p>
Next: <a href="Multi_002dthreaded-Apps.html#Multi_002dthreaded-Apps" accesskey="n" rel="next">Multi-threaded Apps</a>, Previous: <a href="Annotations-for-Running.html#Annotations-for-Running" accesskey="p" rel="prev">Annotations for Running</a>, Up: <a href="index.html#Top" accesskey="u" rel="up">Top</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>]</p>
</div>
<hr>
<a name="Displaying-Source"></a>
<h2 class="chapter">13 Displaying Source</h2>
<a name="index-annotations-for-source-display"></a>

<a name="index-source"></a>
<p>The following annotation is used instead of displaying source code:
</p>
<div class="smallexample">
<pre class="smallexample">^Z^Zsource <var>filename</var>:<var>line</var>:<var>character</var>:<var>middle</var>:<var>addr</var>
</pre></div>

<p>where <var>filename</var> is an absolute file name indicating which source
file, <var>line</var> is the line number within that file (where 1 is the
first line in the file), <var>character</var> is the character position
within the file (where 0 is the first character in the file) (for most
debug formats this will necessarily point to the beginning of a line),
<var>middle</var> is &lsquo;<samp>middle</samp>&rsquo; if <var>addr</var> is in the middle of the
line, or &lsquo;<samp>beg</samp>&rsquo; if <var>addr</var> is at the beginning of the line, and
<var>addr</var> is the address in the target program associated with the
source which is being displayed.  <var>addr</var> is in the form &lsquo;<samp>0x</samp>&rsquo;
followed by one or more lowercase hex digits (note that this does not
depend on the language).
</p>



</body>
</html>
