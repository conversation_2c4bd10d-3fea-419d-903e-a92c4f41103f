<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- This file documents the GNU Assembler "as".

Copyright (C) 1991-2020 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3
or any later version published by the Free Software Foundation;
with no Invariant Sections, with no Front-Cover Texts, and with no
Back-Cover Texts.  A copy of the license is included in the
section entitled "GNU Free Documentation License".
 -->
<!-- Created by GNU Texinfo 6.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Using as: statistics</title>

<meta name="description" content="Using as: statistics">
<meta name="keywords" content="Using as: statistics">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="AS-Index.html#AS-Index" rel="index" title="AS Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Invoking.html#Invoking" rel="up" title="Invoking">
<link href="traditional_002dformat.html#traditional_002dformat" rel="next" title="traditional-format">
<link href="R.html#R" rel="prev" title="R">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.indentedblock {margin-right: 0em}
blockquote.smallindentedblock {margin-right: 0em; font-size: smaller}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smalllisp {margin-left: 3.2em}
kbd {font-style: oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nolinebreak {white-space: nowrap}
span.roman {font-family: initial; font-weight: normal}
span.sansserif {font-family: sans-serif; font-weight: normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en">
<a name="statistics"></a>
<div class="header">
<p>
Next: <a href="traditional_002dformat.html#traditional_002dformat" accesskey="n" rel="next">traditional-format</a>, Previous: <a href="R.html#R" accesskey="p" rel="prev">R</a>, Up: <a href="Invoking.html#Invoking" accesskey="u" rel="up">Invoking</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="AS-Index.html#AS-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Display-Assembly-Statistics_003a-_002d_002dstatistics"></a>
<h3 class="section">2.14 Display Assembly Statistics: <samp>--statistics</samp></h3>

<a name="index-_002d_002dstatistics"></a>
<a name="index-statistics_002c-about-assembly"></a>
<a name="index-time_002c-total-for-assembly"></a>
<a name="index-space-used_002c-maximum-for-assembly"></a>
<p>Use &lsquo;<samp>--statistics</samp>&rsquo; to display two statistics about the resources used by
<code>as</code>: the maximum amount of space allocated during the assembly
(in bytes), and the total execution time taken for the assembly (in <small>CPU</small>
seconds).
</p>



</body>
</html>
