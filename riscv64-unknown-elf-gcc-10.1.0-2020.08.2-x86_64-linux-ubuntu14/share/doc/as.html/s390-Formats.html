<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- This file documents the GNU Assembler "as".

Copyright (C) 1991-2020 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3
or any later version published by the Free Software Foundation;
with no Invariant Sections, with no Front-Cover Texts, and with no
Back-Cover Texts.  A copy of the license is included in the
section entitled "GNU Free Documentation License".
 -->
<!-- Created by GNU Texinfo 6.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Using as: s390 Formats</title>

<meta name="description" content="Using as: s390 Formats">
<meta name="keywords" content="Using as: s390 Formats">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="AS-Index.html#AS-Index" rel="index" title="AS Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="s390-Syntax.html#s390-Syntax" rel="up" title="s390 Syntax">
<link href="s390-Aliases.html#s390-Aliases" rel="next" title="s390 Aliases">
<link href="s390-Operands.html#s390-Operands" rel="prev" title="s390 Operands">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.indentedblock {margin-right: 0em}
blockquote.smallindentedblock {margin-right: 0em; font-size: smaller}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smalllisp {margin-left: 3.2em}
kbd {font-style: oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nolinebreak {white-space: nowrap}
span.roman {font-family: initial; font-weight: normal}
span.sansserif {font-family: sans-serif; font-weight: normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en">
<a name="s390-Formats"></a>
<div class="header">
<p>
Next: <a href="s390-Aliases.html#s390-Aliases" accesskey="n" rel="next">s390 Aliases</a>, Previous: <a href="s390-Operands.html#s390-Operands" accesskey="p" rel="prev">s390 Operands</a>, Up: <a href="s390-Syntax.html#s390-Syntax" accesskey="u" rel="up">s390 Syntax</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="AS-Index.html#AS-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Instruction-Formats"></a>
<h4 class="subsubsection">******** Instruction Formats</h4>
<a name="index-instruction-formats_002c-s390"></a>
<a name="index-s390-instruction-formats"></a>

<p>The Principles of Operation manuals lists 26 instruction formats where
some of the formats have multiple variants. For the &lsquo;<samp>.insn</samp>&rsquo;
pseudo directive the assembler recognizes some of the formats.
Typically, the most general variant of the instruction format is used
by the &lsquo;<samp>.insn</samp>&rsquo; directive.
</p>
<p>The following table lists the abbreviations used in the table of
instruction formats:
</p>
<div class="display">
<table>
<tr><td><pre class="display">OpCode / OpCd</pre></td><td><pre class="display">Part of the op code.</pre></td></tr>
<tr><td><pre class="display">Bx</pre></td><td><pre class="display">Base register number for operand x.</pre></td></tr>
<tr><td><pre class="display">Dx</pre></td><td><pre class="display">Displacement for operand x.</pre></td></tr>
<tr><td><pre class="display">DLx</pre></td><td><pre class="display">Displacement lower 12 bits for operand x.</pre></td></tr>
<tr><td><pre class="display">DHx</pre></td><td><pre class="display">Displacement higher 8-bits for operand x.</pre></td></tr>
<tr><td><pre class="display">Rx</pre></td><td><pre class="display">Register number for operand x.</pre></td></tr>
<tr><td><pre class="display">Xx</pre></td><td><pre class="display">Index register number for operand x.</pre></td></tr>
<tr><td><pre class="display">Ix</pre></td><td><pre class="display">Signed immediate for operand x.</pre></td></tr>
<tr><td><pre class="display">Ux</pre></td><td><pre class="display">Unsigned immediate for operand x.</pre></td></tr>
</table>
</div>

<p>An instruction is two, four, or six bytes in length and must be aligned
on a 2 byte boundary. The first two bits of the instruction specify the
length of the instruction, 00 indicates a two byte instruction, 01 and 10
indicates a four byte instruction, and 11 indicates a six byte instruction.
</p>
<p>The following table lists the s390 instruction formats that are available
with the &lsquo;<samp>.insn</samp>&rsquo; pseudo directive:
</p>
<dl compact="compact">
<dt><code>E format</code></dt>
<dd><pre class="verbatim">+-------------+
|    OpCode   |
+-------------+
0            15
</pre>
</dd>
<dt><code>RI format: &lt;insn&gt; R1,I2</code></dt>
<dd><pre class="verbatim">+--------+----+----+------------------+
| OpCode | R1 |OpCd|        I2        |
+--------+----+----+------------------+
0        <USER>    <GROUP>   16                31
</pre>
</dd>
<dt><code>RIE format: &lt;insn&gt; R1,R3,I2</code></dt>
<dd><pre class="verbatim">+--------+----+----+------------------+--------+--------+
| OpCode | R1 | R3 |        I2        |////////| OpCode |
+--------+----+----+------------------+--------+--------+
0        8    12   16                 32       40      47
</pre>
</dd>
<dt><code>RIL format: &lt;insn&gt; R1,I2</code></dt>
<dd><pre class="verbatim">+--------+----+----+------------------------------------+
| OpCode | R1 |OpCd|                  I2                |
+--------+----+----+------------------------------------+
0        <USER>    <GROUP>   16                                  47
</pre>
</dd>
<dt><code>RILU format: &lt;insn&gt; R1,U2</code></dt>
<dd><pre class="verbatim">+--------+----+----+------------------------------------+
| OpCode | R1 |OpCd|                  U2                |
+--------+----+----+------------------------------------+
0        <USER>    <GROUP>   16                                  47
</pre>
</dd>
<dt><code>RIS format: &lt;insn&gt; R1,I2,M3,D4(B4)</code></dt>
<dd><pre class="verbatim">+--------+----+----+----+-------------+--------+--------+
| OpCode | R1 | M3 | B4 |     D4      |   I2   | Opcode |
+--------+----+----+----+-------------+--------+--------+
0        8    12   16   20            32       36      47
</pre>
</dd>
<dt><code>RR format: &lt;insn&gt; R1,R2</code></dt>
<dd><pre class="verbatim">+--------+----+----+
| OpCode | R1 | R2 |
+--------+----+----+
0        8    12  15
</pre>
</dd>
<dt><code>RRE format: &lt;insn&gt; R1,R2</code></dt>
<dd><pre class="verbatim">+------------------+--------+----+----+
|      OpCode      |////////| R1 | R2 |
+------------------+--------+----+----+
0                  16       24   28  31
</pre>
</dd>
<dt><code>RRF format: &lt;insn&gt; R1,R2,R3,M4</code></dt>
<dd><pre class="verbatim">+------------------+----+----+----+----+
|      OpCode      | R3 | M4 | R1 | R2 |
+------------------+----+----+----+----+
0                  16   20   24   28  31
</pre>
</dd>
<dt><code>RRS format: &lt;insn&gt; R1,R2,M3,D4(B4)</code></dt>
<dd><pre class="verbatim">+--------+----+----+----+-------------+----+----+--------+
| OpCode | R1 | R3 | B4 |     D4      | M3 |////| OpCode |
+--------+----+----+----+-------------+----+----+--------+
0        8    12   16   20            32   36   40      47
</pre>
</dd>
<dt><code>RS format: &lt;insn&gt; R1,R3,D2(B2)</code></dt>
<dd><pre class="verbatim">+--------+----+----+----+-------------+
| OpCode | R1 | R3 | B2 |     D2      |
+--------+----+----+----+-------------+
0        <USER>    <GROUP>   16   20           31
</pre>
</dd>
<dt><code>RSE format: &lt;insn&gt; R1,R3,D2(B2)</code></dt>
<dd><pre class="verbatim">+--------+----+----+----+-------------+--------+--------+
| OpCode | R1 | R3 | B2 |     D2      |////////| OpCode |
+--------+----+----+----+-------------+--------+--------+
0        8    12   16   20            32       40      47
</pre>
</dd>
<dt><code>RSI format: &lt;insn&gt; R1,R3,I2</code></dt>
<dd><pre class="verbatim">+--------+----+----+------------------------------------+
| OpCode | R1 | R3 |                  I2                |
+--------+----+----+------------------------------------+
0        <USER>    <GROUP>   16                                  47
</pre>
</dd>
<dt><code>RSY format: &lt;insn&gt; R1,R3,D2(B2)</code></dt>
<dd><pre class="verbatim">+--------+----+----+----+-------------+--------+--------+
| OpCode | R1 | R3 | B2 |    DL2      |  DH2   | OpCode |
+--------+----+----+----+-------------+--------+--------+
0        8    12   16   20            32       40      47
</pre>
</dd>
<dt><code>RX format: &lt;insn&gt; R1,D2(X2,B2)</code></dt>
<dd><pre class="verbatim">+--------+----+----+----+-------------+
| OpCode | R1 | X2 | B2 |     D2      |
+--------+----+----+----+-------------+
0        <USER>    <GROUP>   16   20           31
</pre>
</dd>
<dt><code>RXE format: &lt;insn&gt; R1,D2(X2,B2)</code></dt>
<dd><pre class="verbatim">+--------+----+----+----+-------------+--------+--------+
| OpCode | R1 | X2 | B2 |     D2      |////////| OpCode |
+--------+----+----+----+-------------+--------+--------+
0        8    12   16   20            32       40      47
</pre>
</dd>
<dt><code>RXF format: &lt;insn&gt; R1,R3,D2(X2,B2)</code></dt>
<dd><pre class="verbatim">+--------+----+----+----+-------------+----+---+--------+
| OpCode | R3 | X2 | B2 |     D2      | R1 |///| OpCode |
+--------+----+----+----+-------------+----+---+--------+
0        8    12   16   20            32   36  40      47
</pre>
</dd>
<dt><code>RXY format: &lt;insn&gt; R1,D2(X2,B2)</code></dt>
<dd><pre class="verbatim">+--------+----+----+----+-------------+--------+--------+
| OpCode | R1 | X2 | B2 |     DL2     |   DH2  | OpCode |
+--------+----+----+----+-------------+--------+--------+
0        8    12   16   20            32   36   40      47
</pre>
</dd>
<dt><code>S format: &lt;insn&gt; D2(B2)</code></dt>
<dd><pre class="verbatim">+------------------+----+-------------+
|      OpCode      | B2 |     D2      |
+------------------+----+-------------+
0                  <USER>   <GROUP>           31
</pre>
</dd>
<dt><code>SI format: &lt;insn&gt; D1(B1),I2</code></dt>
<dd><pre class="verbatim">+--------+---------+----+-------------+
| OpCode |   I2    | B1 |     D1      |
+--------+---------+----+-------------+
0        <USER>         <GROUP>   20           31
</pre>
</dd>
<dt><code>SIY format: &lt;insn&gt; D1(B1),U2</code></dt>
<dd><pre class="verbatim">+--------+---------+----+-------------+--------+--------+
| OpCode |   I2    | B1 |     DL1     |  DH1   | OpCode |
+--------+---------+----+-------------+--------+--------+
0        8         16   20            32   36   40      47
</pre>
</dd>
<dt><code>SIL format: &lt;insn&gt; D1(B1),I2</code></dt>
<dd><pre class="verbatim">+------------------+----+-------------+-----------------+
|      OpCode      | B1 |      D1     |       I2        |
+------------------+----+-------------+-----------------+
0                  <USER>   <GROUP>            32               47
</pre>
</dd>
<dt><code>SS format: &lt;insn&gt; D1(R1,B1),D2(B3),R3</code></dt>
<dd><pre class="verbatim">+--------+----+----+----+-------------+----+------------+
| OpCode | R1 | R3 | B1 |     D1      | B2 |     D2     |
+--------+----+----+----+-------------+----+------------+
0        <USER>    <GROUP>   16   20            32   36          47
</pre>
</dd>
<dt><code>SSE format: &lt;insn&gt; D1(B1),D2(B2)</code></dt>
<dd><pre class="verbatim">+------------------+----+-------------+----+------------+
|      OpCode      | B1 |     D1      | B2 |     D2     |
+------------------+----+-------------+----+------------+
0        <USER>    <GROUP>   16   20            32   36           47
</pre>
</dd>
<dt><code>SSF format: &lt;insn&gt; D1(B1),D2(B2),R3</code></dt>
<dd><pre class="verbatim">+--------+----+----+----+-------------+----+------------+
| OpCode | R3 |OpCd| B1 |     D1      | B2 |     D2     |
+--------+----+----+----+-------------+----+------------+
0        <USER>    <GROUP>   16   20            32   36           47
</pre>
</dd>
</dl>

<p>For the complete list of all instruction format variants see the
Principles of Operation manuals.
</p>
<hr>
<div class="header">
<p>
Next: <a href="s390-Aliases.html#s390-Aliases" accesskey="n" rel="next">s390 Aliases</a>, Previous: <a href="s390-Operands.html#s390-Operands" accesskey="p" rel="prev">s390 Operands</a>, Up: <a href="s390-Syntax.html#s390-Syntax" accesskey="u" rel="up">s390 Syntax</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="AS-Index.html#AS-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
