<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- This file documents the GNU Assembler "as".

Copyright (C) 1991-2020 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3
or any later version published by the Free Software Foundation;
with no Invariant Sections, with no Front-Cover Texts, and with no
Back-Cover Texts.  A copy of the license is included in the
section entitled "GNU Free Documentation License".
 -->
<!-- Created by GNU Texinfo 6.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Using as: s390 Operand Modifier</title>

<meta name="description" content="Using as: s390 Operand Modifier">
<meta name="keywords" content="Using as: s390 Operand Modifier">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="AS-Index.html#AS-Index" rel="index" title="AS Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="s390-Syntax.html#s390-Syntax" rel="up" title="s390 Syntax">
<link href="s390-Instruction-Marker.html#s390-Instruction-Marker" rel="next" title="s390 Instruction Marker">
<link href="s390-Aliases.html#s390-Aliases" rel="prev" title="s390 Aliases">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.indentedblock {margin-right: 0em}
blockquote.smallindentedblock {margin-right: 0em; font-size: smaller}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smalllisp {margin-left: 3.2em}
kbd {font-style: oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nolinebreak {white-space: nowrap}
span.roman {font-family: initial; font-weight: normal}
span.sansserif {font-family: sans-serif; font-weight: normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en">
<a name="s390-Operand-Modifier"></a>
<div class="header">
<p>
Next: <a href="s390-Instruction-Marker.html#s390-Instruction-Marker" accesskey="n" rel="next">s390 Instruction Marker</a>, Previous: <a href="s390-Aliases.html#s390-Aliases" accesskey="p" rel="prev">s390 Aliases</a>, Up: <a href="s390-Syntax.html#s390-Syntax" accesskey="u" rel="up">s390 Syntax</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="AS-Index.html#AS-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Instruction-Operand-Modifier"></a>
<h4 class="subsubsection">9.41.3.6 Instruction Operand Modifier</h4>
<a name="index-instruction-operand-modifier_002c-s390"></a>
<a name="index-s390-instruction-operand-modifier"></a>

<p>If a symbol modifier is attached to a symbol in an expression for an
instruction operand field, the symbol term is replaced with a reference
to an object in the global offset table (GOT) or the procedure linkage
table (PLT). The following expressions are allowed:
&lsquo;<samp>symbol@modifier + constant</samp>&rsquo;,
&lsquo;<samp>symbol@modifier + label + constant</samp>&rsquo;, and
&lsquo;<samp>symbol@modifier - label + constant</samp>&rsquo;.
The term &lsquo;<samp>symbol</samp>&rsquo; is the symbol that will be entered into the GOT or
PLT, &lsquo;<samp>label</samp>&rsquo; is a local label, and &lsquo;<samp>constant</samp>&rsquo; is an arbitrary
expression that the assembler can evaluate to a constant value.
</p>
<p>The term &lsquo;<samp>(symbol + constant1)@modifier +/- label + constant2</samp>&rsquo;
is also accepted but a warning message is printed and the term is
converted to &lsquo;<samp>symbol@modifier +/- label + constant1 + constant2</samp>&rsquo;.
</p>
<dl compact="compact">
<dt><code>@got</code></dt>
<dt><code>@got12</code></dt>
<dd><p>The @got modifier can be used for displacement fields, 16-bit immediate
fields and 32-bit pc-relative immediate fields. The @got12 modifier is
synonym to @got. The symbol is added to the GOT. For displacement
fields and 16-bit immediate fields the symbol term is replaced with
the offset from the start of the GOT to the GOT slot for the symbol.
For a 32-bit pc-relative field the pc-relative offset to the GOT
slot from the current instruction address is used.
</p></dd>
<dt><code>@gotent</code></dt>
<dd><p>The @gotent modifier can be used for 32-bit pc-relative immediate fields.
The symbol is added to the GOT and the symbol term is replaced with
the pc-relative offset from the current instruction to the GOT slot for the
symbol.
</p></dd>
<dt><code>@gotoff</code></dt>
<dd><p>The @gotoff modifier can be used for 16-bit immediate fields. The symbol
term is replaced with the offset from the start of the GOT to the
address of the symbol.
</p></dd>
<dt><code>@gotplt</code></dt>
<dd><p>The @gotplt modifier can be used for displacement fields, 16-bit immediate
fields, and 32-bit pc-relative immediate fields. A procedure linkage
table entry is generated for the symbol and a jump slot for the symbol
is added to the GOT. For displacement fields and 16-bit immediate
fields the symbol term is replaced with the offset from the start of the
GOT to the jump slot for the symbol. For a 32-bit pc-relative field
the pc-relative offset to the jump slot from the current instruction
address is used.
</p></dd>
<dt><code>@plt</code></dt>
<dd><p>The @plt modifier can be used for 16-bit and 32-bit pc-relative immediate
fields. A procedure linkage table entry is generated for the symbol.
The symbol term is replaced with the relative offset from the current
instruction to the PLT entry for the symbol.
</p></dd>
<dt><code>@pltoff</code></dt>
<dd><p>The @pltoff modifier can be used for 16-bit immediate fields. The symbol
term is replaced with the offset from the start of the PLT to the address
of the symbol.
</p></dd>
<dt><code>@gotntpoff</code></dt>
<dd><p>The @gotntpoff modifier can be used for displacement fields. The symbol
is added to the static TLS block and the negated offset to the symbol
in the static TLS block is added to the GOT. The symbol term is replaced
with the offset to the GOT slot from the start of the GOT.
</p></dd>
<dt><code>@indntpoff</code></dt>
<dd><p>The @indntpoff modifier can be used for 32-bit pc-relative immediate
fields. The symbol is added to the static TLS block and the negated offset
to the symbol in the static TLS block is added to the GOT. The symbol term
is replaced with the pc-relative offset to the GOT slot from the current
instruction address.
</p></dd>
</dl>

<p>For more information about the thread local storage modifiers
&lsquo;<samp>gotntpoff</samp>&rsquo; and &lsquo;<samp>indntpoff</samp>&rsquo; see the ELF extension documentation
&lsquo;<samp>ELF Handling For Thread-Local Storage</samp>&rsquo;.
</p>
<hr>
<div class="header">
<p>
Next: <a href="s390-Instruction-Marker.html#s390-Instruction-Marker" accesskey="n" rel="next">s390 Instruction Marker</a>, Previous: <a href="s390-Aliases.html#s390-Aliases" accesskey="p" rel="prev">s390 Aliases</a>, Up: <a href="s390-Syntax.html#s390-Syntax" accesskey="u" rel="up">s390 Syntax</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="AS-Index.html#AS-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
