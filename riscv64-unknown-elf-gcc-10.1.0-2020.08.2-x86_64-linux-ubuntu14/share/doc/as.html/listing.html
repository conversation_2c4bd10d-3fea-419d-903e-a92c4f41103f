<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- This file documents the GNU Assembler "as".

Copyright (C) 1991-2020 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3
or any later version published by the Free Software Foundation;
with no Invariant Sections, with no Front-Cover Texts, and with no
Back-Cover Texts.  A copy of the license is included in the
section entitled "GNU Free Documentation License".
 -->
<!-- Created by GNU Texinfo 6.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Using as: listing</title>

<meta name="description" content="Using as: listing">
<meta name="keywords" content="Using as: listing">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="AS-Index.html#AS-Index" rel="index" title="AS Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Invoking.html#Invoking" rel="up" title="Invoking">
<link href="M.html#M" rel="next" title="M">
<link href="L.html#L" rel="prev" title="L">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.indentedblock {margin-right: 0em}
blockquote.smallindentedblock {margin-right: 0em; font-size: smaller}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smalllisp {margin-left: 3.2em}
kbd {font-style: oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nolinebreak {white-space: nowrap}
span.roman {font-family: initial; font-weight: normal}
span.sansserif {font-family: sans-serif; font-weight: normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en">
<a name="listing"></a>
<div class="header">
<p>
Next: <a href="M.html#M" accesskey="n" rel="next">M</a>, Previous: <a href="L.html#L" accesskey="p" rel="prev">L</a>, Up: <a href="Invoking.html#Invoking" accesskey="u" rel="up">Invoking</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="AS-Index.html#AS-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Configuring-listing-output_003a-_002d_002dlisting"></a>
<h3 class="section">2.8 Configuring listing output: <samp>--listing</samp></h3>

<p>The listing feature of the assembler can be enabled via the command-line switch
&lsquo;<samp>-a</samp>&rsquo; (see <a href="a.html#a">a</a>).  This feature combines the input source file(s) with a
hex dump of the corresponding locations in the output object file, and displays
them as a listing file.  The format of this listing can be controlled by
directives inside the assembler source (i.e., <code>.list</code> (see <a href="List.html#List">List</a>),
<code>.title</code> (see <a href="Title.html#Title">Title</a>), <code>.sbttl</code> (see <a href="Sbttl.html#Sbttl">Sbttl</a>),
<code>.psize</code> (see <a href="Psize.html#Psize">Psize</a>), and
<code>.eject</code> (see <a href="Eject.html#Eject">Eject</a>) and also by the following switches:
</p>
<dl compact="compact">
<dt><code>--listing-lhs-width=&lsquo;<samp>number</samp>&rsquo;</code></dt>
<dd><a name="index-_002d_002dlisting_002dlhs_002dwidth"></a>
<a name="index-Width-of-first-line-disassembly-output"></a>
<p>Sets the maximum width, in words, of the first line of the hex byte dump.  This
dump appears on the left hand side of the listing output.
</p>
</dd>
<dt><code>--listing-lhs-width2=&lsquo;<samp>number</samp>&rsquo;</code></dt>
<dd><a name="index-_002d_002dlisting_002dlhs_002dwidth2"></a>
<a name="index-Width-of-continuation-lines-of-disassembly-output"></a>
<p>Sets the maximum width, in words, of any further lines of the hex byte dump for
a given input source line.  If this value is not specified, it defaults to being
the same as the value specified for &lsquo;<samp>--listing-lhs-width</samp>&rsquo;.  If neither
switch is used the default is to one.
</p>
</dd>
<dt><code>--listing-rhs-width=&lsquo;<samp>number</samp>&rsquo;</code></dt>
<dd><a name="index-_002d_002dlisting_002drhs_002dwidth"></a>
<a name="index-Width-of-source-line-output"></a>
<p>Sets the maximum width, in characters, of the source line that is displayed
alongside the hex dump.  The default value for this parameter is 100.  The
source line is displayed on the right hand side of the listing output.
</p>
</dd>
<dt><code>--listing-cont-lines=&lsquo;<samp>number</samp>&rsquo;</code></dt>
<dd><a name="index-_002d_002dlisting_002dcont_002dlines"></a>
<a name="index-Maximum-number-of-continuation-lines"></a>
<p>Sets the maximum number of continuation lines of hex dump that will be
displayed for a given single line of source input.  The default value is 4.
</p></dd>
</dl>




</body>
</html>
