<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- This file documents the GNU Assembler "as".

Copyright (C) 1991-2020 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3
or any later version published by the Free Software Foundation;
with no Invariant Sections, with no Front-Cover Texts, and with no
Back-Cover Texts.  A copy of the license is included in the
section entitled "GNU Free Documentation License".
 -->
<!-- Created by GNU Texinfo 6.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Using as: s390 Floating Point</title>

<meta name="description" content="Using as: s390 Floating Point">
<meta name="keywords" content="Using as: s390 Floating Point">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="AS-Index.html#AS-Index" rel="index" title="AS Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="S_002f390_002dDependent.html#S_002f390_002dDependent" rel="up" title="S/390-Dependent">
<link href="SCORE_002dDependent.html#SCORE_002dDependent" rel="next" title="SCORE-Dependent">
<link href="s390-Directives.html#s390-Directives" rel="prev" title="s390 Directives">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.indentedblock {margin-right: 0em}
blockquote.smallindentedblock {margin-right: 0em; font-size: smaller}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smalllisp {margin-left: 3.2em}
kbd {font-style: oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nolinebreak {white-space: nowrap}
span.roman {font-family: initial; font-weight: normal}
span.sansserif {font-family: sans-serif; font-weight: normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en">
<a name="s390-Floating-Point"></a>
<div class="header">
<p>
Previous: <a href="s390-Directives.html#s390-Directives" accesskey="p" rel="prev">s390 Directives</a>, Up: <a href="S_002f390_002dDependent.html#S_002f390_002dDependent" accesskey="u" rel="up">S/390-Dependent</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="AS-Index.html#AS-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Floating-Point-13"></a>
<h4 class="subsection">9.41.5 Floating Point</h4>
<a name="index-floating-point_002c-s390"></a>
<a name="index-s390-floating-point"></a>

<p>The assembler recognizes both the <small>IEEE</small> floating-point instruction and
the hexadecimal floating-point instructions. The floating-point constructors
&lsquo;<samp>.float</samp>&rsquo;, &lsquo;<samp>.single</samp>&rsquo;, and &lsquo;<samp>.double</samp>&rsquo; always emit the
<small>IEEE</small> format. To assemble hexadecimal floating-point constants the
&lsquo;<samp>.long</samp>&rsquo; and &lsquo;<samp>.quad</samp>&rsquo; directives must be used.
</p>



</body>
</html>
